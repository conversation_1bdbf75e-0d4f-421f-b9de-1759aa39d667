import React, { useState, useEffect, useRef } from "react";
import {
  FaTimes,
  FaUniversity,
  FaCalendarAlt,
  FaShieldAlt,
  FaCreditCard,
  FaExclamationCircle,
} from "react-icons/fa";

interface ManualAddCardModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (cardDetails: any) => void;
}

const ManualAddCardModal: React.FC<ManualAddCardModalProps> = ({
  isOpen,
  onClose,
  onSave,
}) => {
  const [institution, setInstitution] = useState("");
  const [lastFour, setLastFour] = useState("");
  const [currentBalance, setCurrentBalance] = useState("");
  const [utilizedAmount, setUtilizedAmount] = useState(0);
  const [paymentDueDate, setPaymentDueDate] = useState("");
  const [aprEndDate, setAprEndDate] = useState("");
  const [tags, setTags] = useState("");
  const [cardType, setCardType] = useState("Personal");
  const [creditLimit, setCreditLimit] = useState("");
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const sliderRef = useRef<HTMLInputElement>(null);

  // Recalculate utilization percentage when balance or limit changes
  useEffect(() => {
    if (currentBalance && creditLimit && parseFloat(creditLimit) > 0) {
      const balance = parseFloat(currentBalance);
      const limit = parseFloat(creditLimit);

      // Calculate utilization percentage
      let percentage = Math.round((balance / limit) * 100);

      // Ensure percentage is between 0 and 100
      percentage = Math.max(0, Math.min(100, percentage));

      setUtilizedAmount(percentage);
    }
  }, [currentBalance, creditLimit]);

  // Update slider appearance
  useEffect(() => {
    if (sliderRef.current) {
      const min = parseFloat(sliderRef.current.min);
      const max = parseFloat(sliderRef.current.max);
      const val = utilizedAmount;
      const percentage = ((val - min) * 100) / (max - min);
      sliderRef.current.style.background = `linear-gradient(to right, #2563eb ${percentage}%, #e5e7eb ${percentage}%)`;
      sliderRef.current.value = val.toString();
    }
  }, [utilizedAmount]);

  // Validate form and submit
  const handleSubmit = () => {
    // Reset errors
    const newErrors: { [key: string]: string } = {};

    // Validate fields
    if (!institution) {
      newErrors.institution = "Institution is required";
    }

    if (!lastFour || lastFour.length !== 4) {
      newErrors.lastFour = "Last four digits are required";
    }

    if (currentBalance && creditLimit) {
      const balance = parseFloat(currentBalance);
      const limit = parseFloat(creditLimit);

      if (balance > limit) {
        newErrors.creditLimit =
          "Credit limit must be greater than or equal to the current balance";
      }
    }

    setErrors(newErrors);

    // Only proceed if no errors
    if (Object.keys(newErrors).length === 0) {
      // Convert comma-separated tags to array
      const tagsArray = tags
        .split(",")
        .map((tag) => tag.trim())
        .filter((tag) => tag.length > 0);

      // Basic validation or data formatting can be done here
      onSave({
        institution,
        lastFour,
        currentBalance,
        utilizedAmount,
        paymentDueDate,
        aprEndDate,
        tags: tagsArray,
        cardType,
        creditLimit,
      });
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 h-full w-full z-50 flex justify-center items-center p-4 sm:p-6 md:p-8">
      <div className="bg-white p-6 rounded-xl shadow-xl w-full max-w-2xl relative flex flex-col max-h-[95vh] sm:max-h-[90vh]">
        <div className="flex-shrink-0">
          <button
            onClick={onClose}
            className="absolute top-4 right-4 sm:top-5 sm:right-5 text-gray-400 hover:text-gray-600 transition-colors z-10"
          >
            <FaTimes size={20} />
          </button>
          <h2 className="text-xl sm:text-2xl font-semibold text-gray-900 mb-1 sm:mb-2">
            Add New Credit Card
          </h2>
          <p className="text-xs sm:text-sm text-gray-500 mb-4 sm:mb-6">
            Enter your credit card details below
          </p>
        </div>

        <div className="overflow-y-auto flex-grow pr-2 mr-[-8px]">
          <form
            onSubmit={(e) => {
              e.preventDefault();
              handleSubmit();
            }}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
              <div className="relative">
                <label
                  htmlFor="institution"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Institution*
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaUniversity className="text-gray-400" />
                  </div>
                  <select
                    id="institution"
                    value={institution}
                    onChange={(e) => setInstitution(e.target.value)}
                    className={`block w-full pl-10 pr-3 py-2.5 border ${
                      errors.institution ? "border-red-500" : "border-gray-300"
                    } rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                  >
                    <option value="">Select Institution</option>
                    <option value="Chase">Chase</option>
                    <option value="Amex">American Express</option>
                    <option value="Citi">Citibank</option>
                    <option value="BoA">Bank of America</option>
                  </select>
                </div>
                {errors.institution && (
                  <p className="mt-1 text-xs text-red-500 flex items-center">
                    <FaExclamationCircle className="mr-1" />
                    {errors.institution}
                  </p>
                )}
              </div>

              <div>
                <label
                  htmlFor="lastFour"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Last Four Digits of Card Number*
                </label>
                <input
                  type="text"
                  id="lastFour"
                  value={lastFour}
                  onChange={(e) =>
                    setLastFour(
                      e.target.value.replace(/[^0-9]/g, "").slice(0, 4)
                    )
                  }
                  placeholder="e.g. 1234"
                  className={`block w-full px-3 py-2.5 border ${
                    errors.lastFour ? "border-red-500" : "border-gray-300"
                  } rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                />
                {errors.lastFour && (
                  <p className="mt-1 text-xs text-red-500 flex items-center">
                    <FaExclamationCircle className="mr-1" />
                    {errors.lastFour}
                  </p>
                )}
              </div>

              <div>
                <label
                  htmlFor="creditLimit"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Credit Limit
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500 sm:text-sm">$</span>
                  </div>
                  <input
                    type="text"
                    id="creditLimit"
                    value={creditLimit}
                    onChange={(e) => {
                      const value = e.target.value.replace(/[^0-9.]/g, "");
                      setCreditLimit(value);
                    }}
                    placeholder="0.00"
                    className={`block w-full pl-7 pr-3 py-2.5 border ${
                      errors.creditLimit ? "border-red-500" : "border-gray-300"
                    } rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                  />
                </div>
                {errors.creditLimit && (
                  <p className="mt-1 text-xs text-red-500 flex items-center">
                    <FaExclamationCircle className="mr-1" />
                    {errors.creditLimit}
                  </p>
                )}
              </div>

              <div>
                <label
                  htmlFor="currentBalance"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Current Balance
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500 sm:text-sm">$</span>
                  </div>
                  <input
                    type="text"
                    id="currentBalance"
                    value={currentBalance}
                    onChange={(e) => {
                      const value = e.target.value.replace(/[^0-9.]/g, "");
                      setCurrentBalance(value);
                    }}
                    placeholder="0.00"
                    className="block w-full pl-7 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              </div>

              <div className="md:col-span-2">
                <div className="flex justify-between items-center mb-1">
                  <label
                    htmlFor="utilizedAmount"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Utilized Amount (Percentage)
                  </label>
                  <span className="text-sm font-medium text-gray-700">
                    {utilizedAmount}%
                  </span>
                </div>
                <input
                  type="range"
                  id="utilizedAmount"
                  ref={sliderRef}
                  min="0"
                  max="100"
                  value={utilizedAmount}
                  onChange={(e) => {
                    setUtilizedAmount(Number(e.target.value));
                  }}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-blue-600 custom-slider"
                  disabled={!creditLimit || parseFloat(creditLimit) <= 0}
                />
                <p className="mt-1 text-xs text-gray-500">
                  Utilization is automatically calculated based on your balance
                  and credit limit
                </p>
              </div>

              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Payment Due Date
                </label>
                <div className="relative">
                  <input
                    type="date"
                    value={paymentDueDate}
                    onChange={(e) => setPaymentDueDate(e.target.value)}
                    className="w-full border border-gray-300 rounded-md shadow-sm py-2.5 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 pr-10 text-sm text-gray-700"
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <FaCalendarAlt className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              </div>

              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  0% APR End Date
                </label>
                <div className="relative">
                  <input
                    type="date"
                    value={aprEndDate}
                    onChange={(e) => setAprEndDate(e.target.value)}
                    className="w-full border border-gray-300 rounded-md shadow-sm py-2.5 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 pr-10 text-sm text-gray-700"
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <FaCalendarAlt className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              </div>

              <div className="md:col-span-2">
                <label
                  htmlFor="tags"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Tags
                </label>
                <input
                  type="text"
                  id="tags"
                  value={tags}
                  onChange={(e) => setTags(e.target.value)}
                  placeholder="Add tags (comma-separated, e.g.: Travel, Dining, Cashback)"
                  className="block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Popular tags: Travel, Dining, Points, Hotels, Cashback,
                  Rotating
                </p>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Card Type
                </label>
                <div className="flex items-center space-x-6">
                  <label
                    htmlFor="personal"
                    className="flex items-center cursor-pointer"
                  >
                    <input
                      type="radio"
                      id="personal"
                      name="cardType"
                      value="Personal"
                      checked={cardType === "Personal"}
                      onChange={(e) => setCardType(e.target.value)}
                      className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Personal</span>
                  </label>
                  <label
                    htmlFor="business"
                    className="flex items-center cursor-pointer"
                  >
                    <input
                      type="radio"
                      id="business"
                      name="cardType"
                      value="Business"
                      checked={cardType === "Business"}
                      onChange={(e) => setCardType(e.target.value)}
                      className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Business</span>
                  </label>
                </div>
              </div>
            </div>
          </form>
        </div>

        <div className="flex-shrink-0 mt-auto pt-4 border-t border-gray-200">
          <div className="flex flex-col sm:flex-row items-center justify-between space-y-3 sm:space-y-0">
            <div className="flex items-center text-xs sm:text-sm text-gray-600">
              <FaShieldAlt className="mr-2 text-blue-600" />
              Your data is encrypted and secure
            </div>
            <div className="flex items-center space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-5 py-2 sm:px-6 sm:py-2.5 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                onClick={handleSubmit}
                className="px-5 py-2 sm:px-6 sm:py-2.5 bg-[#16c66c] border border-transparent rounded-md text-sm font-medium text-white hover:bg-[#238e4e] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors flex items-center"
              >
                <FaCreditCard className="mr-1.5 sm:mr-2" /> Save Card
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ManualAddCardModal;
