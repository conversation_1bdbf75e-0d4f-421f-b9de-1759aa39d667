import React, { useState, useEffect } from "react";
import {
  FaChevronDown,
  FaChevronUp,
  FaPlus,
  FaSync,
  FaEllipsisV,
  FaFilter,
  FaDownload,
  FaLandmark,
  FaCreditCard,
} from "react-icons/fa";
import { quilttApi } from "@/services/quilttApi";
import { IoMdAdd } from "react-icons/io";
import AddBankAccountModal from "./AddBankAccountModal";

// Define a type for linked credit cards
interface LinkedCreditCard {
  id: string;
  name: string;
  icon: string; // Could be a short name or an actual icon component/URL
}

interface BankAccount {
  id: string;
  name: string; // Account name like "Checking Account", "Savings Plus"
  type: string; // Raw type from API like "checking", "savings"
  verified: boolean;
  institution: {
    name: string; // Bank name like "Bank of America"
  };
  balance: {
    id: string;
    at: string; // Last synced timestamp (can be used for "Last Synced")
    available: number;
    current: number;
    limit?: number;
  };
  // Fields to be added for UI, some might be dummy for now
  bankIcon: string; // e.g., "BOA", "WF"
  lastFourDigits: string;
  linkedCreditCards: LinkedCreditCard[];
  tags: string[];
}

// Dummy credit card data
const dummyCreditCards: Record<string, LinkedCreditCard[]> = {
  "1": [
    { id: "cc1", name: "Chase Sapphire Reserve", icon: "C" },
    { id: "cc2", name: "Chase Freedom Flex", icon: "C" },
    { id: "cc3", name: "Chase Freedom Unlimited", icon: "C" },
  ],
  "2": [{ id: "cc4", name: "Wells Fargo Autograph", icon: "W" }],
  "3": [],
  "4": [
    { id: "cc5", name: "Capital One Venture X", icon: "C" },
    { id: "cc6", name: "Capital One SavorOne", icon: "C" },
  ],
};

const getBankIconInfo = (
  institutionName: string
): { shortName: string; color: string; icon: React.ElementType } => {
  if (institutionName.toLowerCase().includes("bank of america"))
    return { shortName: "BOA", color: "bg-red-600", icon: FaLandmark };
  if (institutionName.toLowerCase().includes("wells fargo"))
    return { shortName: "WF", color: "bg-yellow-500", icon: FaLandmark };
  if (institutionName.toLowerCase().includes("chase"))
    return { shortName: "CHASE", color: "bg-[#16c66c]", icon: FaLandmark };
  if (institutionName.toLowerCase().includes("capital one"))
    return { shortName: "CO", color: "bg-blue-700", icon: FaLandmark };
  return {
    shortName: institutionName.substring(0, 2).toUpperCase(),
    color: "bg-gray-500",
    icon: FaLandmark,
  };
};

const BankAccountsList: React.FC = () => {
  const [accounts, setAccounts] = useState<BankAccount[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [expandedRow, setExpandedRow] = useState<string | null>(null);

  const fetchAccounts = async () => {
    setLoading(true);
    setError(null);
    try {
      // Get the auth token and ensure we have a valid Quiltt token first
      const auth_token = localStorage.getItem("auth_token");
      await quilttApi.getToken(auth_token || "");

      // Now fetch accounts using the established Quiltt token
      const apiAccounts = await quilttApi.getAccounts();

      // Map API accounts to our BankAccount interface with dummy data
      const enrichedAccounts = apiAccounts.map((acc, index) => ({
        ...acc,
        bankIcon: getBankIconInfo(acc.institution.name).shortName,
        // Dummy data for now
        lastFourDigits: `****${1234 + index}`,
        linkedCreditCards: dummyCreditCards[(index % 4) + 1] || [],
        tags: index % 2 === 0 ? ["Personal"] : ["Business", "Travel"],
      }));
      setAccounts(enrichedAccounts);
    } catch (err) {
      console.error("Error fetching accounts:", err);
      setError("Failed to load your bank accounts. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAccounts();
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatAccountType = (type: string) => {
    if (!type) return "N/A";
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  const formatLastSynced = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return "Just now";
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60)
      return `${diffInMinutes} minute${diffInMinutes > 1 ? "s" : ""} ago`;
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24)
      return `${diffInHours} hour${diffInHours > 1 ? "s" : ""} ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} day${diffInDays > 1 ? "s" : ""} ago`;
  };

  const handleAddAccountSuccess = () => {
    setIsModalOpen(false);
    fetchAccounts(); // Refresh accounts list
  };

  const toggleRow = (id: string) => {
    setExpandedRow(expandedRow === id ? null : id);
  };

  return (
    <div className="max-w-full mx-auto py-6">
      <div className="flex justify-between items-center mb-6 px-4 sm:px-0">
        <h1 className="text-xl font-semibold text-gray-800">
          Your Bank Accounts
        </h1>
        <div className="flex items-center space-x-3">
          <button className="p-2 text-gray-500 hover:text-gray-700">
            <FaFilter size={18} />
          </button>
          <button className="p-2 text-gray-500 hover:text-gray-700">
            <FaDownload size={18} />
          </button>
          <select className="border border-gray-300 rounded-md text-sm px-3 py-1.5 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
            <option>Accounts</option>
            <option>Transactions</option>
          </select>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      ) : error ? (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6 mx-4 sm:mx-0 rounded">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      ) : accounts.length === 0 ? (
        <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200 text-center mx-4 sm:mx-0">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No bank accounts connected
          </h3>
          <p className="text-gray-600 mb-4">
            Connect your bank account to manage your finances in one place.
          </p>
          <button
            onClick={() => setIsModalOpen(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-[#16c66c] hover:bg-[#238e4e] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <IoMdAdd className="mr-2" />
            Add Bank Account
          </button>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-sm overflow-x-auto border border-gray-200">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Bank Name
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Account Type
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Last 4 Digits
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Balance
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Linked Credit Cards
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Tags
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Last Synced
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {accounts.map((account) => {
                const iconInfo = getBankIconInfo(account.institution.name);
                return (
                  <React.Fragment key={account.id}>
                    <tr
                      className={`hover:bg-gray-50 ${expandedRow === account.id ? "bg-gray-50" : ""}`}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div
                            className={`flex-shrink-0 h-7 w-7 rounded-md ${iconInfo.color} text-white flex items-center justify-center text-sm font-medium`}
                          >
                            <iconInfo.icon size={14} />
                          </div>
                          <div className="ml-3">
                            <div className="text-sm font-medium text-gray-800">
                              {account.institution.name}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2.5 py-0.5 inline-flex text-xs leading-5 font-semibold rounded-full ${account.type.toLowerCase() === "checking" ? "bg-blue-100 text-blue-700" : "bg-green-100 text-green-700"}`}
                        >
                          {formatAccountType(account.type)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {account.lastFourDigits}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800">
                        {formatCurrency(account.balance?.current || 0)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                        <div className="flex items-center">
                          <span className="mr-1.5 px-1.5 py-0.5 bg-blue-500 text-white text-xs font-semibold rounded-full h-5 w-5 flex items-center justify-center">
                            {account.linkedCreditCards.length}
                          </span>
                          <span className="mr-2">Cards</span>
                          <button
                            onClick={() => toggleRow(account.id)}
                            className="text-gray-400 hover:text-gray-600"
                          >
                            {expandedRow === account.id ? (
                              <FaChevronUp size={12} />
                            ) : (
                              <FaChevronDown size={12} />
                            )}
                          </button>
                          <button className="ml-2 p-1 rounded-full hover:bg-gray-200 text-gray-500 hover:text-blue-600">
                            <FaPlus size={14} />
                          </button>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {account.tags.map((tag, idx) => (
                          <span
                            key={idx}
                            className={`px-2.5 py-0.5 rounded-full text-xs font-medium mr-1.5 ${idx % 2 === 0 ? "bg-purple-100 text-purple-700" : "bg-pink-100 text-pink-700"}`}
                          >
                            {tag}
                          </span>
                        ))}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center text-sm text-gray-500">
                          {formatLastSynced(account.balance.at)}
                          <span
                            className={`ml-2 h-2 w-2 rounded-full ${account.verified ? "bg-green-500" : "bg-yellow-400"}`}
                          ></span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <button className="text-gray-400 hover:text-gray-600 p-1">
                          <FaEllipsisV size={16} />
                        </button>
                      </td>
                    </tr>
                    {expandedRow === account.id && (
                      <tr>
                        <td colSpan={8} className="py-3 px-6 bg-gray-50">
                          <div className="pl-10 space-y-2">
                            {account.linkedCreditCards.length > 0 ? (
                              account.linkedCreditCards.map((card) => (
                                <div
                                  key={card.id}
                                  className="flex items-center justify-between p-2.5 bg-white rounded-md border border-gray-200 shadow-sm hover:shadow-md transition-shadow"
                                >
                                  <div className="flex items-center">
                                    <div className="flex-shrink-0 h-6 w-6 rounded-full bg-blue-500 text-white flex items-center justify-center text-xs font-medium mr-3">
                                      {card.icon}
                                    </div>
                                    <span className="text-sm text-gray-700 font-medium">
                                      {card.name}
                                    </span>
                                    <button className="ml-2 text-gray-400 hover:text-blue-600">
                                      <FaCreditCard
                                        size={14}
                                        className="inline"
                                      />
                                      <span className="text-xs underline ml-1">
                                        View Details
                                      </span>
                                    </button>
                                  </div>
                                  <button className="text-gray-400 hover:text-red-600 p-1">
                                    <FaSync size={14} />{" "}
                                    {/* Placeholder for unlink, ideally different icon */}
                                  </button>
                                </div>
                              ))
                            ) : (
                              <div className="text-sm text-gray-500 py-2">
                                No credit cards linked to this account.
                              </div>
                            )}
                          </div>
                        </td>
                      </tr>
                    )}
                  </React.Fragment>
                );
              })}
            </tbody>
          </table>
          <div className="px-6 py-3 bg-white border-t border-gray-200 text-sm text-gray-500 flex justify-between items-center">
            <div>
              Showing {accounts.length} of {accounts.length} accounts
            </div>
            <div className="flex items-center space-x-1">
              <button
                className="px-3 py-1 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 text-xs disabled:opacity-50"
                disabled
              >
                &lt; Prev
              </button>
              <button
                className="px-3 py-1 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 text-xs disabled:opacity-50"
                disabled
              >
                Next &gt;
              </button>
            </div>
          </div>
        </div>
      )}

      <AddBankAccountModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSuccess={handleAddAccountSuccess}
      />
    </div>
  );
};

export default BankAccountsList;
