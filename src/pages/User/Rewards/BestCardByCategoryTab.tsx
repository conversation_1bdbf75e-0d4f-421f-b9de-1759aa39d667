import React, { useState } from "react";
import { FaChevronDown, <PERSON>a<PERSON><PERSON><PERSON>, FaSearch } from "react-icons/fa";

interface CardDetail {
  id: string;
  name: string;
  type: string;
  icon: string;
  backgroundColor: string;
  bestFor?: string;
  bestForColor?: string;
  categories: {
    name: string;
    value: string;
  }[];
  annualFee: string;
}

const BestCardByCategoryTab: React.FC = () => {
  const [sortOption, setSortOption] = useState("Highest Rewards");
  const [selectedCategory, setSelectedCategory] = useState("All Categories");

  const categories = [
    { id: "all", name: "All Categories" },
    { id: "travel", name: "Travel" },
    { id: "dining", name: "Dining" },
    { id: "shopping", name: "Shopping" },
    { id: "entertainment", name: "Entertainment" },
    { id: "gas", name: "Gas" },
    { id: "groceries", name: "Groceries" },
  ];

  const cards: CardDetail[] = [
    {
      id: "1",
      name: "Chase Sapphire",
      type: "Preferred",
      icon: "CS",
      backgroundColor: "bg-blue-500",
      bestFor: "Best for Travel",
      bestForColor: "bg-blue-100 text-blue-800",
      categories: [
        { name: "Travel Rewards", value: "3x points" },
        { name: "Dining", value: "2x points" },
        { name: "Other Purchases", value: "1x points" },
      ],
      annualFee: "$95",
    },
    {
      id: "2",
      name: "Amex Gold",
      type: "Card",
      icon: "AG",
      backgroundColor: "bg-yellow-500",
      bestFor: "Best for Dining",
      bestForColor: "bg-yellow-100 text-yellow-800",
      categories: [
        { name: "Restaurants", value: "4x points" },
        { name: "Groceries", value: "4x points" },
        { name: "Other Purchases", value: "1x points" },
      ],
      annualFee: "$250",
    },
    {
      id: "3",
      name: "Citi Double Cash",
      type: "Card",
      icon: "CD",
      backgroundColor: "bg-purple-500",
      bestFor: "Best for Cashback",
      bestForColor: "bg-green-100 text-green-800",
      categories: [
        { name: "All Purchases", value: "2% cashback" },
        { name: "On Purchase", value: "1%" },
        { name: "When You Pay", value: "1%" },
      ],
      annualFee: "$0",
    },
  ];

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <div className="flex items-center justify-center w-9 h-9 rounded-md bg-blue-100">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="currentColor"
              className="w-5 h-5 text-blue-600"
            >
              <path d="M4.5 3.75a3 3 0 00-3 3v.75h21v-.75a3 3 0 00-3-3h-15z" />
              <path
                fillRule="evenodd"
                d="M22.5 9.75h-21v7.5a3 3 0 003 3h15a3 3 0 003-3v-7.5zm-18 3.75a.75.75 0 01.75-.75h6a.75.75 0 010 1.5h-6a.75.75 0 01-.75-.75zm.75 2.25a.75.75 0 000 1.5h3a.75.75 0 000-1.5h-3z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <h2 className="text-lg font-medium text-gray-800">
            Best Card by Category
          </h2>
        </div>

        <div className="flex space-x-3">
          <div className="relative">
            <input
              type="text"
              placeholder="Search categories (e.g., Travel, Dining, Shopping)"
              className="w-64 pl-9 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>

          <div className="relative">
            <select
              className="appearance-none pl-3 pr-8 py-2 bg-white border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={sortOption}
              onChange={(e) => setSortOption(e.target.value)}
            >
              <option>Highest Rewards</option>
              <option>Lowest Annual Fee</option>
              <option>Alphabetical</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
              <FaChevronDown className="h-3 w-3" />
            </div>
          </div>

          <button className="p-2 text-gray-500 hover:bg-gray-100 rounded-md">
            <FaFilter size={16} />
          </button>
        </div>
      </div>

      {/* Category Filters */}
      <div className="flex flex-wrap gap-2 mb-6">
        {categories.map((category) => (
          <button
            key={category.id}
            className={`px-4 py-2 text-sm rounded-md ${
              selectedCategory === category.name
                ? "bg-[#16c66c] text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
            onClick={() => setSelectedCategory(category.name)}
          >
            {category.name}
          </button>
        ))}
      </div>

      {/* Cards List */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {cards.map((card) => (
          <div
            key={card.id}
            className="bg-white rounded-md border border-gray-200 overflow-hidden"
          >
            <div className="p-4">
              <div className="flex items-center mb-4">
                <div
                  className={`w-10 h-10 ${card.backgroundColor} text-white rounded-md flex items-center justify-center mr-3`}
                >
                  {card.icon}
                </div>
                <div>
                  <div className="font-medium">{card.name}</div>
                  <div className="text-sm text-gray-500">{card.type}</div>
                </div>
              </div>

              {card.bestFor && (
                <div
                  className={`inline-block px-3 py-1 rounded-md text-xs font-medium ${card.bestForColor} mb-3`}
                >
                  {card.bestFor}
                </div>
              )}

              <div className="space-y-3 mb-4">
                {card.categories.map((category, idx) => (
                  <div key={idx} className="flex justify-between items-center">
                    <div className="text-sm text-gray-700">{category.name}</div>
                    <div className="text-sm font-medium">{category.value}</div>
                  </div>
                ))}
              </div>

              <div className="mt-4 pt-3 border-t border-gray-100 flex justify-between items-center">
                <div className="text-sm text-gray-700">Annual Fee</div>
                <div className="text-sm font-medium">{card.annualFee}</div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 text-center">
        <button className="text-blue-600 border border-blue-200 rounded-md px-4 py-2 text-sm hover:bg-blue-50 flex items-center mx-auto">
          <span>Load more cards</span>
          <FaChevronDown className="ml-2" size={12} />
        </button>
      </div>
    </div>
  );
};

export default BestCardByCategoryTab;
