import React from "react";
import {
  FaTrophy,
  FaClock,
  FaChartLine,
  FaArrowUp,
  FaCreditCard,
  FaBriefcase,
  FaExclamationTriangle,
  FaChartPie,
} from "react-icons/fa";

const OverviewTab: React.FC = () => {
  // Sample data for the rewards trend chart
  const monthlyData = [
    { month: "JAN", value: 400 },
    { month: "FEB", value: 600 },
    { month: "MAR", value: 800 },
    { month: "APR", value: 850 },
    { month: "MAY", value: 900 },
    { month: "JUN", value: 1200 },
  ];

  // Calculate the max value for scaling the chart
  const maxValue = Math.max(...monthlyData.map((d) => d.value));
  const creditUsage = 70; // 70% used for example

  return (
    <div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
        {/* Total Rewards */}
        <div className="bg-white rounded-md border border-gray-200 p-4 sm:p-5 relative">
          <div className="text-gray-500 text-sm mb-1">Total Rewards</div>
          <div className="text-gray-500 text-xs mb-4">Across all cards</div>

          <div className="absolute top-5 right-5 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
            <FaTrophy className="text-blue-500" />
          </div>

          <div className="text-3xl font-medium mb-1">
            5,200{" "}
            <span className="text-sm font-normal text-gray-500">Points</span>
          </div>

          <div className="flex items-center text-green-600 text-sm">
            <FaArrowUp className="mr-1" size={12} />
            $20 pts this month
          </div>

          <div className="mt-4 text-sm text-gray-600">
            Next Goal: $50 Gift Card
            <div className="mt-1 bg-gray-200 rounded-full h-1.5">
              <div
                className="bg-[#16c66c] h-1.5 rounded-full"
                style={{ width: "60%" }}
              ></div>
            </div>
            <div className="mt-1 text-right text-xs text-gray-500">
              500 points left
            </div>
          </div>
        </div>

        {/* Expiring Rewards */}
        <div className="bg-white rounded-md border border-gray-200 p-4 sm:p-5 relative">
          <div className="text-gray-500 text-sm mb-1">Expiring Rewards</div>
          <div className="text-gray-500 text-xs mb-4">In the next 60 days</div>

          <div className="absolute top-5 right-5 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
            <FaClock className="text-red-500" />
          </div>

          <div className="space-y-4">
            <div className="flex items-start">
              <div className="w-8 h-8 rounded-md bg-blue-500 text-white flex items-center justify-center mr-3 mt-1">
                <span className="text-xs">CS</span>
              </div>
              <div>
                <div className="text-sm font-medium">Chase Sapphire</div>
                <div className="text-xs text-gray-500">1,200 points</div>
                <div className="mt-1 text-xs text-red-500">30 days left</div>
              </div>
            </div>

            <div className="flex items-start">
              <div className="w-8 h-8 rounded-md bg-purple-500 text-white flex items-center justify-center mr-3 mt-1">
                <span className="text-xs">AG</span>
              </div>
              <div>
                <div className="text-sm font-medium">Amex Gold</div>
                <div className="text-xs text-gray-500">800 points</div>
                <div className="mt-1 text-xs text-orange-500">45 days left</div>
              </div>
            </div>
          </div>

          <button className="mt-4 w-full py-2 text-center text-blue-600 border border-blue-200 rounded-md text-sm hover:bg-blue-50">
            View All Expiring Rewards
          </button>
        </div>

        {/* Rewards Trend */}
        <div className="bg-white rounded-md border border-gray-200 p-4 sm:p-5 relative">
          <div className="text-gray-500 text-sm mb-1">Rewards Trend</div>
          <div className="text-gray-500 text-xs mb-4">
            Monthly rewards earned
          </div>

          <div className="absolute top-5 right-5 w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
            <FaChartLine className="text-green-500" />
          </div>

          <div className="h-40 flex items-end space-x-2 sm:space-x-4 mt-4">
            {monthlyData.map((data, index) => (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div
                  className={`w-full bg-blue-${index === monthlyData.length - 1 ? "600" : "200"} rounded-t-sm`}
                  style={{ height: `${(data.value / maxValue) * 100}%` }}
                ></div>
                <div className="mt-2 text-xs text-gray-500">{data.month}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OverviewTab;
