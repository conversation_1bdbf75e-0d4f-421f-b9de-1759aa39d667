import React, { useState } from "react";
import { FaChevronDown, FaStar, FaSearch } from "react-icons/fa";

interface Perk {
  id: string;
  title: string;
  description: string;
  icon: string;
  backgroundColor: string;
  card: string;
  cardColor: string;
  tags: {
    name: string;
    color: string;
  }[];
}

const PerksAndBenefitsTab: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedBenefit, setSelectedBenefit] = useState("All Benefits");

  const perks: Perk[] = [
    {
      id: "1",
      title: "Airport Lounge Access",
      description:
        "Free access to over 1,000 airport lounges worldwide including Priority Pass and Centurion Lounges.",
      icon: "✈️",
      backgroundColor: "bg-blue-500",
      card: "Amex Platinum",
      cardColor: "bg-blue-500",
      tags: [{ name: "Travel", color: "bg-blue-100 text-blue-800" }],
    },
    {
      id: "2",
      title: "Travel Insurance",
      description:
        "Comprehensive travel insurance including trip cancellation, baggage delay, and emergency assistance.",
      icon: "🛡️",
      backgroundColor: "bg-green-500",
      card: "Chase Sapphire",
      cardColor: "bg-green-500",
      tags: [{ name: "Insurance", color: "bg-green-100 text-green-800" }],
    },
    {
      id: "3",
      title: "Dining Credits",
      description:
        "$120 annual dining credit at select restaurants and food delivery services, applied as $10 monthly credits.",
      icon: "🍴",
      backgroundColor: "bg-purple-500",
      card: "Amex Gold",
      cardColor: "bg-purple-500",
      tags: [{ name: "Dining", color: "bg-purple-100 text-purple-800" }],
    },
    {
      id: "4",
      title: "Extended Warranty",
      description:
        "Extends the manufacturer's warranty by up to 2 years on eligible purchases made with your card.",
      icon: "🕒",
      backgroundColor: "bg-amber-500",
      card: "Citi Double Cash",
      cardColor: "bg-amber-500",
      tags: [{ name: "Shopping", color: "bg-yellow-100 text-yellow-800" }],
    },
    {
      id: "5",
      title: "Price Protection",
      description:
        "If you find a lower price on an item within 90 days of purchase, you can be reimbursed the difference.",
      icon: "🏷️",
      backgroundColor: "bg-red-500",
      card: "Discover It",
      cardColor: "bg-red-500",
      tags: [{ name: "Shopping", color: "bg-red-100 text-red-800" }],
    },
    {
      id: "6",
      title: "Global Entry/TSA PreCheck",
      description:
        "Statement credit of up to $100 every 4 years for Global Entry or TSA PreCheck application fee.",
      icon: "🌐",
      backgroundColor: "bg-blue-500",
      card: "Chase Sapphire",
      cardColor: "bg-blue-500",
      tags: [{ name: "Travel", color: "bg-blue-100 text-blue-800" }],
    },
  ];

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <div className="flex items-center justify-center w-9 h-9 rounded-md bg-amber-100">
            <span className="text-amber-600 text-xl">💎</span>
          </div>
          <h2 className="text-lg font-medium text-gray-800">
            Perks & Benefits
          </h2>
        </div>

        <div className="flex space-x-3">
          <button className="bg-yellow-100 text-yellow-600 px-4 py-2 rounded-md text-sm flex items-center">
            <FaStar className="mr-2" />
            Favorites
          </button>

          <div className="relative w-56">
            <input
              type="text"
              placeholder="Search card"
              className="w-full pl-3 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="relative">
            <select
              className="appearance-none pl-3 pr-8 py-2 bg-white border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={selectedBenefit}
              onChange={(e) => setSelectedBenefit(e.target.value)}
            >
              <option>All Benefits</option>
              <option>Travel</option>
              <option>Dining</option>
              <option>Shopping</option>
              <option>Insurance</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
              <FaChevronDown className="h-3 w-3" />
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {perks.map((perk) => (
          <div
            key={perk.id}
            className="bg-white rounded-md border border-gray-200 overflow-hidden relative"
          >
            <div
              className={`h-36 ${perk.backgroundColor} relative flex items-center justify-center`}
            >
              <span className="text-white text-5xl">{perk.icon}</span>
              <button className="absolute top-2 left-2 text-white rounded-full p-1.5 hover:bg-white hover:bg-opacity-20">
                <FaStar className="h-5 w-5" />
              </button>
              <div className="absolute top-2 right-2 px-3 py-1 rounded-md text-sm text-white bg-black bg-opacity-20">
                {perk.card}
              </div>
            </div>
            <div className="p-4">
              <h3 className="font-medium text-gray-800 text-lg mb-2">
                {perk.title}
              </h3>
              <p className="text-sm text-gray-600 mb-4">{perk.description}</p>
              <div className="flex flex-wrap gap-2">
                {perk.tags.map((tag, idx) => (
                  <div
                    key={idx}
                    className={`${tag.color} px-3 py-1 rounded-full text-xs`}
                  >
                    {tag.name}
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 text-center">
        <button className="text-blue-600 border border-blue-200 rounded-md px-4 py-2 text-sm hover:bg-blue-50 flex items-center mx-auto">
          <span>View all perks & benefits</span>
          <FaChevronDown className="ml-2" size={12} />
        </button>
      </div>
    </div>
  );
};

export default PerksAndBenefitsTab;
