import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  FaExchangeAlt,
  FaExclamationTriangle,
  FaPlane,
  FaLock,
} from "react-icons/fa";

interface Recommendation {
  id: string;
  type: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  iconBg: string;
  cards?: string[];
  action?: {
    text: string;
    variant: "default" | "warning";
  };
  match?: number;
}

interface CardRecommendation {
  id: string;
  name: string;
  type: string;
  icon: string;
  description: string;
  match: number;
}

const AIRecommendationsTab: React.FC = () => {
  const insights: Recommendation[] = [
    {
      id: "1",
      type: "Spending Insight",
      title:
        "You're spending 30% of your income on dining. Use your Amex Gold for these purchases to earn 4x points, potentially adding 1,200 more points per month.",
      description: "",
      icon: <FaChartPie className="h-5 w-5 text-blue-600" />,
      iconBg: "bg-blue-100",
      cards: ["Amex Gold"],
      action: {
        text: "Set as default for dining",
        variant: "default",
      },
    },
    {
      id: "2",
      type: "Balance Transfer Opportunity",
      title:
        "Transfer $3,500 from Chase Sapphire to Brex Business Card with 0% APR for 12 months to save approximately $420 in interest.",
      description: "",
      icon: <FaExchangeAlt className="h-5 w-5 text-green-600" />,
      iconBg: "bg-green-100",
      cards: ["Chase Sapphire", "Brex Business Card"],
      action: {
        text: "View transfer plan",
        variant: "default",
      },
    },
    {
      id: "3",
      type: "Best Card for Travel: Chase Sapphire",
      title:
        "With your upcoming summer vacation, use Chase Sapphire to earn 3x points on travel and get complimentary travel insurance.",
      description: "",
      icon: <FaPlane className="h-5 w-5 text-blue-600" />,
      iconBg: "bg-blue-100",
      cards: ["Chase Sapphire"],
      action: {
        text: "Set as default for travel",
        variant: "default",
      },
    },
    {
      id: "4",
      type: "0% APR Ending Soon",
      title:
        "Your Chase Sapphire 0% APR ends in 30 days. To avoid paying 18.99% APR, consider a balance transfer or setting up a payment plan.",
      description: "",
      icon: <FaExclamationTriangle className="h-5 w-5 text-red-600" />,
      iconBg: "bg-red-100",
      cards: ["Chase Sapphire"],
      action: {
        text: "View options",
        variant: "warning",
      },
    },
  ];

  const cardRecommendations: CardRecommendation[] = [
    {
      id: "1",
      name: "Capital One Venture",
      type: "Travel Rewards Card",
      icon: "📊",
      description:
        "Based on your travel spending, this card would earn you approximately 25,000 more points annually compared to your current cards.",
      match: 94,
    },
    {
      id: "2",
      name: "Amazon Prime Rewards",
      type: "Cash Back Card",
      icon: "💲",
      description:
        "With your Amazon shopping habits, this card would earn you approximately $320 more in cashback annually.",
      match: 89,
    },
  ];

  return (
    <div>
      <div className="flex items-center mb-6">
        <div className="flex items-center justify-center w-9 h-9 rounded-md bg-blue-100 mr-2">
          <FaRobot className="h-5 w-5 text-blue-600" />
        </div>
        <h2 className="text-lg font-medium text-gray-800">
          AI Recommendations
        </h2>
      </div>

      <div className="bg-blue-50 border border-blue-100 rounded-md p-4 mb-8">
        <h3 className="text-lg font-medium text-gray-800 mb-2">
          Smart Rewards Optimization
        </h3>
        <p className="text-gray-600 text-sm">
          Based on your spending patterns and card benefits, here are
          personalized recommendations to maximize your rewards.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {insights.map((insight) => (
          <div
            key={insight.id}
            className={`bg-white rounded-md border ${insight.type.includes("APR") ? "border-red-200" : "border-gray-200"} p-4`}
          >
            <div className="flex items-start mb-4">
              <div
                className={`${insight.iconBg} p-2 rounded-full mr-3 flex-shrink-0 mt-1`}
              >
                {insight.icon}
              </div>
              <div>
                <div className="text-sm text-gray-500 mb-1">{insight.type}</div>
                <div className="text-gray-800">
                  {insight.title
                    .split(/\b(Amex Gold|Chase Sapphire|Brex Business Card)\b/)
                    .map((part, i) =>
                      [
                        "Amex Gold",
                        "Chase Sapphire",
                        "Brex Business Card",
                      ].includes(part) ? (
                        <span key={i} className="font-medium">
                          {part}
                        </span>
                      ) : (
                        part
                      )
                    )}
                </div>
              </div>
            </div>

            {insight.cards && insight.cards.length > 0 && (
              <div className="flex -space-x-2 mb-3">
                {insight.cards.map((card, idx) => (
                  <div
                    key={idx}
                    className={`w-8 h-8 rounded-full ${card.includes("Chase") ? "bg-blue-500" : card.includes("Amex") ? "bg-purple-500" : "bg-green-500"} flex items-center justify-center text-white text-xs border-2 border-white`}
                  >
                    {card.includes("Chase")
                      ? "CS"
                      : card.includes("Amex")
                        ? "AG"
                        : "BB"}
                  </div>
                ))}
              </div>
            )}

            {insight.action && (
              <button
                className={`text-sm ${
                  insight.action.variant === "warning"
                    ? "text-red-600 hover:text-red-700"
                    : "text-blue-600 hover:text-blue-700"
                } font-medium`}
              >
                {insight.action.text}
              </button>
            )}
          </div>
        ))}
      </div>

      <h3 className="text-lg font-medium text-gray-800 mb-4">
        New Card Recommendations
      </h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {cardRecommendations.map((card) => (
          <div
            key={card.id}
            className="bg-white rounded-md border border-gray-200 p-4"
          >
            <div className="flex items-start mb-4">
              <div
                className={`w-10 h-10 bg-blue-100 rounded-md flex items-center justify-center mr-3`}
              >
                <span className="text-lg">
                  {card.name.includes("Capital") ? "✈️" : "💰"}
                </span>
              </div>
              <div>
                <div className="font-medium">{card.name}</div>
                <div className="text-sm text-gray-500">{card.type}</div>
              </div>
            </div>

            <p className="text-sm text-gray-600 mb-4">{card.description}</p>

            <div className="flex justify-end">
              <div
                className={`px-2 py-1 rounded-md text-xs ${card.match >= 90 ? "bg-green-100 text-green-700" : "bg-blue-100 text-blue-700"}`}
              >
                {card.match}% Match
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AIRecommendationsTab;
