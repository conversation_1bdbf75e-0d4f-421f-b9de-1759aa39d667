import React from "react";
import { FaChevronDown, FaFilter } from "react-icons/fa";

interface RewardCard {
  id: string;
  name: string;
  type: string;
  icon: string;
  color: string;
  totalPoints?: number;
  totalCashback?: number;
  value?: string;
  rates: {
    category: string;
    rate: string;
  }[];
  pending?: string;
  utilized?: boolean;
  effectiveRate?: string;
}

const MyRewardsTab: React.FC = () => {
  const [sortOption, setSortOption] = React.useState("Highest Rewards");
  const [selectedFilter, setSelectedFilter] = React.useState("All Rewards");

  const rewardCards: RewardCard[] = [
    {
      id: "1",
      name: "Chase Sapphire",
      type: "Preferred",
      icon: "CS",
      color: "bg-blue-500",
      totalPoints: 3500,
      value: "$43.75",
      rates: [
        { category: "Dining", rate: "2x points" },
        { category: "Travel", rate: "3x points" },
        { category: "All other purchases", rate: "1x points" },
      ],
      utilized: true,
    },
    {
      id: "2",
      name: "Citi Double Cash",
      type: "Card",
      icon: "CD",
      color: "bg-orange-500",
      totalCashback: 200,
      pending: "$35",
      rates: [
        { category: "On purchases", rate: "1% cashback" },
        { category: "When you pay", rate: "1% cashback" },
      ],
      effectiveRate: "2% cashback",
    },
    {
      id: "3",
      name: "Amex Gold",
      type: "Card",
      icon: "AG",
      color: "bg-purple-500",
      totalPoints: 1500,
      value: "$30.00",
      rates: [
        { category: "Dining", rate: "4x points" },
        { category: "Groceries", rate: "4x points" },
        { category: "All other purchases", rate: "1x points" },
      ],
      utilized: true,
    },
  ];

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <div className="flex items-center justify-center w-9 h-9 rounded-md bg-blue-100">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="currentColor"
              className="w-5 h-5 text-blue-600"
            >
              <path d="M11.25 4.533A9.707 9.707 0 006 3a9.735 9.735 0 00-3.25.555.75.75 0 00-.5.707v14.25a.75.75 0 001 .707A8.237 8.237 0 016 18.75c1.995 0 3.823.707 5.25 1.886V4.533zM12.75 20.636A8.214 8.214 0 0118 18.75c.966 0 1.89.166 2.75.47a.75.75 0 001-.708V4.262a.75.75 0 00-.5-.707A9.735 9.735 0 0018 3a9.707 9.707 0 00-5.25 1.533v16.103z" />
            </svg>
          </div>
          <h2 className="text-lg font-medium text-gray-800">My Rewards</h2>
        </div>

        <div className="flex space-x-3">
          <div className="relative">
            <select
              className="appearance-none pl-3 pr-8 py-2 bg-white border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={sortOption}
              onChange={(e) => setSortOption(e.target.value)}
            >
              <option>Highest Rewards</option>
              <option>Lowest Rewards</option>
              <option>Newest Cards</option>
              <option>Oldest Cards</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
              <FaChevronDown className="h-3 w-3" />
            </div>
          </div>

          <div className="relative">
            <select
              className="appearance-none pl-3 pr-8 py-2 bg-white border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value)}
            >
              <option>All Rewards</option>
              <option>Points Only</option>
              <option>Cashback Only</option>
              <option>Utilized</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
              <FaChevronDown className="h-3 w-3" />
            </div>
          </div>

          <button className="p-2 text-gray-500 hover:bg-gray-100 rounded-md">
            <FaFilter size={16} />
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {rewardCards.map((card) => (
          <div
            key={card.id}
            className="bg-white rounded-md border border-gray-200 overflow-hidden"
          >
            <div className="p-4 border-b border-gray-100">
              <div className="flex items-center mb-4">
                <div
                  className={`w-10 h-10 ${card.color} text-white rounded-md flex items-center justify-center mr-3`}
                >
                  {card.icon}
                </div>
                <div>
                  <div className="font-medium">{card.name}</div>
                  <div className="text-sm text-gray-500">{card.type}</div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-3">
                <div>
                  <div className="text-sm text-gray-500">
                    {card.totalPoints ? "Total Points" : "Total Cashback"}
                  </div>
                  <div className="text-xl font-bold">
                    {card.totalPoints
                      ? card.totalPoints.toLocaleString()
                      : `$${card.totalCashback}`}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">
                    {card.value ? "Value" : "Pending"}
                  </div>
                  <div className="text-xl font-bold text-green-600">
                    {card.value || card.pending}
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                {card.rates.map((rate, idx) => (
                  <div key={idx} className="flex justify-between">
                    <div className="text-sm text-gray-600">{rate.category}</div>
                    <div className="text-sm font-medium">{rate.rate}</div>
                  </div>
                ))}

                {card.effectiveRate && (
                  <div className="flex justify-between mt-2 pt-2 border-t border-gray-100">
                    <div className="text-sm font-medium text-gray-700">
                      Total effective rate
                    </div>
                    <div className="text-sm font-bold">
                      {card.effectiveRate}
                    </div>
                  </div>
                )}
              </div>

              {card.utilized !== undefined && (
                <div className="mt-4 flex">
                  <div
                    className={`px-2 py-1 rounded-full text-xs ${card.utilized ? "bg-green-100 text-green-700" : "bg-gray-100 text-gray-600"}`}
                  >
                    {card.utilized ? "Utilized" : "Unutilized"}
                  </div>
                </div>
              )}
            </div>

            <div className="px-4 py-3 bg-gray-50 text-center">
              <button className="text-sm text-blue-600 font-medium hover:text-blue-700">
                View Details
              </button>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 text-center">
        <button className="text-blue-600 border border-blue-200 rounded-md px-4 py-2 text-sm hover:bg-blue-50 flex items-center mx-auto">
          <span>View all reward cards</span>
          <FaChevronDown className="ml-2" size={12} />
        </button>
      </div>
    </div>
  );
};

export default MyRewardsTab;
