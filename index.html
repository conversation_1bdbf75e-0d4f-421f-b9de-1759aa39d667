<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/png" href="/icons/maskable-icon-512x512.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <!-- League Spartan: Free geometric sans serif similar to Futura/Avenir -->
    <link
      href="https://fonts.googleapis.com/css2?family=League+Spartan:wght@100;200;300;400;500;600;700;800;900&display=swap"
      rel="stylesheet"
    />
    <title>Wireframe Tool V5 | Manaknight Digital</title>
    <!-- <script>window.global = window;</script>
    <meta http-equiv="Content-Security-Policy"
      content="script-src 'sha256-pQY0fuQAnnVQH5nQfjo80rzGkQzeN3JeAtAJ+1KcD4k=' 'self' blob: https://cdnjs.cloudflare.com;"> -->
    <!-- <meta http-equiv="Content-Security-Policy"
      content="script-src 'sha256-/AO8vAagk08SqUGxY96ci/dGyTDsuoetPOJYMn7sc+E=' 'self' blob:"> -->

    <!-- Hotjar Tracking Code for Site 5128711 (name missing) -->
    <script>
      (function (h, o, t, j, a, r) {
        h.hj =
          h.hj ||
          function () {
            (h.hj.q = h.hj.q || []).push(arguments);
          };
        h._hjSettings = { hjid: 5128711, hjsv: 6 };
        a = o.getElementsByTagName("head")[0];
        r = o.createElement("script");
        r.async = 1;
        r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
        a.appendChild(r);
      })(window, document, "https://static.hotjar.com/c/hotjar-", ".js?sv=");
    </script>
  </head>
  <body style="width: 100% !important">
    <div id="root"></div>
    <!-- <div id="portal"></div> -->

    <script type="module" src="/src/index.tsx"></script>
  </body>
</html>
